"""
iICrawlerMCP - A LangChain-based web crawler with Playwright integration.

This package provides tools for web crawling using LangChain agents and Playwright browser automation.
"""

__version__ = "0.1.0"
__author__ = "iICrawlerMCP Team"

# Core components
from .core import Browser, get_global_browser, close_global_browser, config

# DOM extraction
from .dom import DOMExtractor, ElementInfo

# Agents
from .agents import (
    WebAgent,
    build_web_agent,
)

# Tools
from .tools import (
    BrowserToolkit,
    get_tools,
    get_browser_specific_tools,
    cleanup_tools,
    # Basic browser tools (legacy compatibility)
    navigate_browser as navigate,
    take_screenshot as screenshot,
    analyze_screenshot,
    get_page_info,
    click_element as browser_click,
    type_text as browser_type,
    hover_element,
    scroll_page,
    # Advanced browser tools
    browser_snapshot,
    click_element_advanced,
    select_option,
    press_key,
    wait_for_element,
)

# MCP Server (optional)
try:
    from .mcp import MCPServer, start_mcp_server, intelligent_web_task

    _MCP_AVAILABLE = True
except ImportError:
    _MCP_AVAILABLE = False
    MCPServer = None
    start_mcp_server = None
    intelligent_web_task = None

__all__ = [
    # Core
    "Browser",
    "get_global_browser",
    "close_global_browser",
    "config",
    # DOM
    "DOMExtractor",
    "ElementInfo",
    # Agents
    "WebAgent",
    "build_web_agent",
    # Tools
    "BrowserToolkit",
    "get_tools",
    "get_browser_specific_tools",
    "cleanup_tools",
    # Legacy compatibility
    "navigate",
    "screenshot",
    "analyze_screenshot",
    "get_page_info",
    "browser_click",
    "browser_type",
    "hover_element",
    "scroll_page",
    # Advanced tools
    "browser_snapshot",
    "click_element_advanced",
    "select_option",
    "press_key",
    "wait_for_element",
    # MCP (optional)
    "MCPServer",
    "start_mcp_server",
    "intelligent_web_task",
    "_MCP_AVAILABLE",
]
