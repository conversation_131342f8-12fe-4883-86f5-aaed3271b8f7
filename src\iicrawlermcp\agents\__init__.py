"""
Agents module for iICrawlerMCP.

This module contains various AI agents for web automation tasks.
"""

from .web_agent import WebAgent, build_web_agent
from .codegen_agent import CodeGenAgent, build_codegen_agent
from .smart_planner_agent import (
    SmartPlannerAgent,
    build_smart_planner_agent,
    quick_analyze,
    quick_plan,
)

__all__ = [
    "WebAgent",
    "build_web_agent",
    "CodeGenAgent",
    "build_codegen_agent",
    # SmartPlannerAgent (unified planning and optimization)
    "SmartPlannerAgent",
    "build_smart_planner_agent",
    "quick_analyze",
    "quick_plan",
]
