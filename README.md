# iICrawlerMCP

一个基于LangChain和Playwright的智能网页爬虫系统，采用强制的"计划→执行→验证"三阶段工作流，确保所有任务都经过完整的质量控制流程。

## 🎯 核心特点

**智能编排机制**：所有任务自动经过 SmartPlannerAgent（规划）→ WebAgent（执行）→ CodeGenAgent（代码生成）的完整流程，确保任务完成质量。

## 💡 为什么选择 iICrawlerMCP？

### 质量保证机制
- **智能验证**：WebAgent内置自我验证机制，确保任务完成质量
- **智能补充**：执行失败时自动识别缺失项并生成补充执行计划
- **端到端保障**：从任务理解到代码生成的全流程质量控制

### 智能化程度高
- **自动规划**：SmartPlannerAgent将复杂任务自动分解为可执行步骤
- **智能路由**：根据任务类型自动选择最合适的Agent执行
- **自适应执行**：根据执行结果动态调整后续步骤

### 架构优势
- **职责分离**：每个Agent专注单一领域，提高专业性和可靠性
- **松耦合设计**：Agent之间通过标准化工具通信，易于扩展
- **纯协调模式**：CrawlerAgent不含业务逻辑，专注流程编排

## ✨ 主要特性

### 当前版本 (v1.1) - 增强的多Agent协作系统
- 🎯 **智能流程编排**: 所有任务经过"规划→执行→代码生成"三阶段
- 📋 **智能任务规划**: SmartPlannerAgent自动分解任务并生成执行计划
- ✅ **内置质量保证**: WebAgent内置自我验证机制，确保任务完成质量
- 🤖 **三大专业Agent**: 覆盖智能规划、Web执行、代码生成
- 🔄 **智能重试机制**: 执行失败时自动生成补充计划并重新执行
- 🌐 **强大浏览器控制**: 基于Playwright的完整浏览器自动化能力
- 🧠 **LLM驱动决策**: 使用OpenAI GPT-4进行智能决策
- 🛡️ **类型安全**: 完整的类型提示和错误处理
- 🎨 **Streamlit UI**: 实时显示Agent思考过程和执行结果

## 🏗️ 系统架构

### 核心架构图

```mermaid
graph TB
    User[用户] -->|任务描述| SmartPlanner[SmartPlannerAgent<br/>智能规划专家]

    SmartPlanner -->|执行计划| Web[WebAgent<br/>Web自动化执行]

    Web -->|自我验证| Decision{任务完成?}

    Decision -->|继续执行| Web
    Decision -->|任务完成| CodeGen[CodeGenAgent<br/>代码生成专家]

    CodeGen -->|生成代码| Result[返回结果]

    Result -->|最终报告| User

    style SmartPlanner fill:#e8f5e8,stroke:#333,stroke-width:2px
    style Web fill:#e1f5fe,stroke:#333,stroke-width:2px
    style CodeGen fill:#fff3e0,stroke:#333,stroke-width:2px
    style Decision fill:#f3e5f5,stroke:#333,stroke-width:2px
```

### 工作流程说明

1. **任务接收**：CrawlerAgent作为纯协调器接收用户任务
2. **自动编排**：通过`smart_task_orchestrator`工具触发完整流程
3. **智能规划**：SmartPlannerAgent分析任务，生成结构化执行计划
4. **智能执行**：WebAgent根据计划执行任务，内置自我验证
5. **代码生成**：CodeGenAgent将操作历史转换为可复用的Playwright代码
6. **自动补充**：未完成时自动生成补充计划并重新执行
7. **结果返回**：返回包含计划、执行过程和生成代码的完整报告

## 🤖 核心Agent介绍

### 协调层
- **CrawlerAgent**: 纯协调器，通过委托工具调用其他Agent，不包含业务逻辑

### 核心Agent层
- **SmartPlannerAgent**: 智能规划专家，整合任务规划和提示词优化功能
- **WebAgent**: Web自动化专家，负责浏览器操作、DOM分析和任务验证
- **CodeGenAgent**: 代码生成专家，将操作历史转换为Playwright代码

## 🔧 核心工具

### 编排工具（最高优先级）
- **smart_task_orchestrator**: 完整的计划→执行→验证流程编排器
- **smart_task_planner**: 独立的任务规划工具
- **smart_task_validator**: 独立的结果验证工具

### 委托工具
- **smart_element_finder**: 智能元素查找，委托给WebAgent
- **smart_browser_action_finder**: 智能浏览器操作，委托给WebAgent
- **smart_prompt_optimizer**: 智能提示词优化，委托给SmartPlannerAgent

### 基础工具包
- **BrowserToolkit**: 16个浏览器操作工具（导航、点击、输入、截图等）
- **DOM Tools Enhanced**: 10个精确DOM分析工具（元素查找、数据提取）
- **Prompt Optimization Tools**: 8个提示词优化工具（清晰度、结构化等）

## 🚀 快速安装

### 📋 前置要求

- Python 3.11 或更高版本
- Node.js (用于Playwright浏览器安装)
- OpenAI API Key

### 📦 一键安装

```bash
# 1. 克隆项目
git clone https://git.dev.sh.ctripcorp.com/octopus/icrawlermcp.git
cd icrawlermcp

# 2. 安装依赖
pip install -r requirements.txt
playwright install

# 3. 配置环境
echo "OPENAI_API_KEY=your_openai_api_key_here" > .env

# 4. 验证安装
python -c "from iicrawlermcp.agents import build_agent; print('✅ 安装成功!')"
```

### ⚙️ 环境配置

在项目根目录创建 `.env` 文件:

```env
# 必需配置
OPENAI_API_KEY=your_openai_api_key_here

# 可选配置
OPENAI_MODEL=gpt-4-turbo          # 模型选择
HEADLESS=true                     # 无头模式
VERBOSE=true                      # 详细日志
DEFAULT_SCREENSHOT_PATH=screenshots/  # 截图路径
```

## ⚙️ 配置说明

在项目根目录创建 `.env` 文件进行配置:

```env
# 必需: OpenAI API配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_API_BASE=https://api.openai.com/v1  # 可选: 自定义API地址
OPENAI_MODEL=gpt-4-turbo  # 可选: 模型名称

# 可选: 浏览器配置
HEADLESS=true  # 无头模式运行浏览器
BROWSER_TIMEOUT=60000  # 浏览器超时时间(毫秒)

# 可选: 应用配置
DEFAULT_SCREENSHOT_PATH=screenshots/  # 默认截图路径
VERBOSE=true  # 启用详细日志
```

## 📈 最新改进 (v1.1)

### 🧠 智能提示词优化系统升级

- **全LLM驱动**: 所有优化工具使用大模型进行智能分析
- **智能触发**: 6种触发条件，自动检测优化需求，调用频率提升300%+
- **自适应学习**: 基于历史数据和用户反馈持续改进
- **闭环反馈**: 完整的监控、分析、优化循环
- **预防性优化**: 任务执行前主动优化，避免失败
- **失败恢复**: 自动优化重试机制

详细信息请查看：[提示词优化系统增强改进](docs/improvements/prompt-optimization-enhancement.md)

## 🎯 快速开始

### 🎨 方式1: LangGraph Studio（推荐可视化调试）

```bash
# 启动 LangGraph Studio
python run_studio.py

# 访问界面: https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024
```

**特性**:
- 🔍 可视化工作流执行过程
- 🐛 实时调试和状态检查
- 📊 执行步骤和数据流可视化
- ⚙️ 交互式任务执行

### 🤖 方式2: Python API（直接编程使用）

```python
from iicrawlermcp.agents import build_agent

# 创建主协调Agent
agent = build_agent()

# 任何任务都会自动经过计划→执行→验证流程
result = agent.invoke("""
    导航到google.com，搜索'人工智能'，
    点击第一个非广告结果，然后截图保存
""")

# 结果包含完整的执行报告
# {
#   "task": "原始任务描述",
#   "plan": "SmartPlannerAgent生成的执行计划",
#   "execution_results": [各步骤执行结果],
#   "generated_code": "CodeGenAgent生成的代码",
#   "final_status": "completed/incomplete"
# }

print(result["output"])
agent.cleanup()
```

### 📋 工作流程示例

```python
# 示例：复杂任务的自动编排
agent = build_agent()

# 系统会自动：
# 1. SmartPlannerAgent 分析任务，生成计划
# 2. WebAgent 导航到网站并查找登录表单
# 3. WebAgent 填写表单并自我验证
# 4. CodeGenAgent 生成可复用的自动化代码
# 5. 如果失败，自动重新规划并执行

result = agent.invoke("登录example.com并下载用户报告")
```

### 🛰️ 方式3: MCP 服务器（适合集成）

```bash
# 启动 MCP 服务器
python src/iicrawlermcp/mcp/run_server.py sse --host 127.0.0.1 --port 8000

# 使用 MCP 客户端调用
python examples/mcp/call_mcp.py
```

### 🌐 专门Agent使用

```python
from iicrawlermcp.agents import build_browser_agent, build_element_agent

# 浏览器控制专家 - 专注浏览器操作
browser_agent = build_browser_agent()
browser_agent.invoke("导航到https://google.com并截图")

# DOM分析专家 - 专注元素分析
element_agent = build_element_agent()
element_agent.invoke("分析页面上的所有按钮和输入框")
```

### 🔧 工具包直接使用

```python
from iicrawlermcp.tools.browser_tools import BrowserToolkit
from iicrawlermcp.core.browser import get_global_browser

# 方式1: 使用工具包
basic_tools = BrowserToolkit.get_basic_tools()
advanced_tools = BrowserToolkit.get_advanced_tools()

# 方式2: 直接使用浏览器
browser = get_global_browser()
browser.navigate("https://example.com")
screenshot_path = browser.screenshot()
print(f"截图保存到: {screenshot_path}")
```

### 📱 示例和测试

```bash
# 运行基础示例
python examples/basic/browser_basics.py

# 运行高级示例
python examples/advanced/multi_agent_collaboration.py

# 运行测试
pytest tests/
```

### 🛰️ MCP 服务器启动与调用

> v1.0 起，本项目内置 **MCP (Model Context Protocol) Server**，
> 你可以把多-Agent 能力通过标准协议暴露为一个远程服务，
> 供外部应用或其它语言调用。

#### 1. 启动服务器

```bash
# 进入项目根目录
cd iICrawlerMCP

# （1）本机调试，仅监听 127.0.0.1:8000，SSE 传输
python src/iicrawlermcp/mcp/run_server.py sse --host 127.0.0.1 --port 8000

# （2）局域网 / 公网可访问，监听全部网卡
python src/iicrawlermcp/mcp/run_server.py sse --host 0.0.0.0 --port 8000

# （3）Streamable-HTTP 方式（可选）
python src/iicrawlermcp/mcp/run_server.py http --host 0.0.0.0 --port 8000
```

启动后：

* SSE 地址: `http://<host>:8000/sse`
* HTTP 地址: `http://<host>:8000/mcp`

#### 2. 使用示例脚本调用 MCP

项目已提供 `examples/mcp/call_mcp.py`，默认连接 `http://127.0.0.1:8000/sse`，并调用公开的 `intelligent_web_task`。

```bash
python examples/mcp/call_mcp.py
# 预期输出：
# [MCP 返回结果]
#  ... 智能任务执行结果 ...
```

#### 3. 可视化调试 (MCP Inspector)

如果你想在浏览器里实时查看流式输出，可以使用官方 GUI：

```bash
npx @modelcontextprotocol/inspector
# 传入 SSE URL → Connect
```

#### 4. 停止服务

按 `Ctrl + C` 即可安全关闭服务器，浏览器实例会自动在后台线程中清理。

## 📁 项目结构

```
iICrawlerMCP/
├── src/iicrawlermcp/           # 🏠 主包
│   ├── agents/                 # 🤖 Agent实现
│   │   ├── crawler_agent.py    #   纯协调Agent
│   │   ├── planner_agent.py    #   任务规划Agent ⭐新增
│   │   ├── validator_agent.py  #   结果验证Agent ⭐新增
│   │   ├── browser_agent.py    #   浏览器专家Agent
│   │   ├── element_agent.py    #   DOM分析专家Agent
│   │   └── prompt_agent.py     #   提示词优化Agent
│   ├── tools/                  # 🔧 工具层
│   │   ├── browser_tools.py    #   统一浏览器工具包（14个工具）
│   │   ├── dom_tools.py        #   精确DOM分析工具（12个工具）
│   │   ├── smart_planner_tools.py # 智能规划工具（4个统一工具）
│   │   ├── prompt_tools.py     #   提示词优化工具（4个工具）
│   │   ├── codegen_tools.py    #   代码生成工具（3个专业工具）
│   │   └── search_tools.py     #   网络搜索工具（1个工具）
│   │   └── prompt_optimization_tools.py # 提示优化工具
│   ├── prompts/                # 📝 提示词模板
│   │   └── coordinator_prompt.py #  协调器专用提示词 ⭐新增
│   ├── core/                   # ⚙️ 核心层
│   │   ├── browser.py          #   浏览器封装
│   │   ├── handoff_engine.py   #   Agent移交引擎
│   │   ├── optimization_trigger.py # 优化触发器
│   │   └── config.py           #   配置管理
│   ├── dom/                    # 📄 DOM处理
│   │   └── core/
│   │       └── extractor.py    #   DOM提取器
│   └── mcp/                    # 🛰️ MCP集成
│       ├── server.py           #   MCP服务器实现
│       └── run_server.py       #   服务器启动脚本
├── examples/                   # 💡 示例代码
├── tests/                      # 🧪 测试代码
├── screenshots/                # 📸 截图输出
├── docs/                       # 📚 文档
└── README.md                   # 📖 本文档
```


### 📚 核心文档

| 文档 | 描述 | 适用人群 |
|------|------|----------|
| [📖 文档中心](docs/README.md) | 所有文档的导航和索引 | 所有用户 |
| [🚀 快速上手](docs/user-guide/quick-start.md) | 5分钟快速体验指南 | 新用户 |
| [🎨 Streamlit UI 指南](docs/user-guide/streamlit-ui.md) | 图形化界面使用和开发指南 | 新用户、开发者 |
| [🏗️ 系统架构](docs/architecture/system-architecture.md) | 系统架构和技术实现 | 开发者、架构师 |
| [🎯 LangGraph分层监督架构](docs/architecture/langgraph-hierarchical.md) | 推荐的新架构方案 | 架构师、技术决策者 |
| [📋 项目路线图](docs/project/status-and-roadmap.md) | 项目状态和未来规划 | 项目管理者、开发者 |
| [🔧 开发指南](docs/development/setup.md) | 开发环境搭建和贡献说明 | 贡献者 |

## 🚀 未来规划

### 近期改进（v1.2）
- 增强任务规划的智能化程度
- 优化验证规则，减少不必要的重试
- 提升Agent间协作效率
- 扩展更多专业Agent（数据处理、文件管理等）

### 长期愿景（v2.0）
- 迁移到LangGraph工作流引擎
- 支持操作录制和代码生成
- 实现更复杂的并行执行策略
- 提供可视化工作流编辑器

## 🧪 测试和验证

```bash
# 快速验证安装
python -c "from iicrawlermcp.agents import build_agent; print('✅ 系统正常')"

# 运行完整测试套件
pytest tests/ --cov=src/iicrawlermcp

# 运行示例验证功能
python examples/basic/browser_basics.py
python examples/advanced/multi_agent_collaboration.py
```

## 🎯 典型使用场景

### 🌐 网页自动化（带质量保证）
```python
# 系统自动规划→执行→代码生成，确保任务完成
agent.invoke("填写并提交*******************的联系表单")
# SmartPlannerAgent: 生成4步计划（导航、查找表单、填写、提交）
# WebAgent: 按计划执行各步骤并自我验证
# CodeGenAgent: 生成可复用的自动化代码
```

### 🤖 智能数据采集
```python
# 复杂的多步骤爬虫任务，自动处理失败和重试
agent.invoke("爬取电商网站前10个商品的价格和评论")
# 自动处理：分页、动态加载、数据验证、失败重试
```

### 📊 自动化测试
```python
# 端到端的测试流程，包含验证和报告
agent.invoke("测试登录流程是否正常工作")
# 包含：测试计划生成、执行步骤、结果验证、测试报告
```

## 🛠️ 开发工具

```bash
# 代码格式化
black src/ tests/

# 代码检查
flake8 src/ tests/

# 类型检查
mypy src/
```

## 🚫 故障排除

### 常见问题

| 问题 | 解决方案 |
|------|----------|
| 🚫 Playwright浏览器未安装 | `playwright install` |
| 🔑 OpenAI API密钥错误 | 检查 `.env` 文件中的 `OPENAI_API_KEY` |
| 📦 导入错误 | 确保从项目根目录运行 |
| 🤖 Agent创建失败 | 检查配置和依赖是否正确安装 |
| 📸 截图保存失败 | 确保 `screenshots/` 目录存在且有写权限 |

### 获取帮助

- 📖 查看 [ARCHITECTURE.md](ARCHITECTURE.md) 了解系统设计
- 📋 查看 [API_REFERENCE.md](API_REFERENCE.md) 了解详细API
- 💡 运行 [examples/](examples/) 中的示例代码
- 🧪 查看 [tests/](tests/) 了解最佳实践

## 🤝 贡献指南

我们欢迎所有形式的贡献！

### 贡献方式
1. � **报告Bug**: 创建Issue描述问题
2. ✨ **功能建议**: 提出新功能想法
3. � **改进文档**: 完善文档和示例
4. � **代码贡献**: 提交Pull Request

### 开发流程
```bash
# 1. Fork并克隆项目
git clone https://git.dev.sh.ctripcorp.com/octopus/icrawlermcp.git

# 2. 创建功能分支
git checkout -b feature/amazing-feature

# 3. 安装开发依赖
pip install -r requirements-dev.txt

# 4. 进行开发和测试
pytest tests/

# 5. 提交更改
git commit -m "Add amazing feature"
git push origin feature/amazing-feature

# 6. 创建Pull Request
```

详细开发指南请查看 [DEVELOPMENT.md](DEVELOPMENT.md)

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 🌟 致谢

感谢以下开源项目的支持：
- [LangChain](https://github.com/langchain-ai/langchain) - Agent框架
- [Playwright](https://github.com/microsoft/playwright-python) - 浏览器自动化
- [OpenAI](https://openai.com/) - 大语言模型服务

---

**iICrawlerMCP v1.1** - 智能编排，质量保证，让每个网页自动化任务都完美完成 🚀

*通过强制的"计划→执行→验证"流程，我们确保每个任务都达到预期效果*

*如果这个项目对你有帮助，请给我们一个 ⭐ Star！*
