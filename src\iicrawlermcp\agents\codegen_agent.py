"""
CodeGen Agent module for generating Playwright automation code.

这个模块提供专业的代码生成Agent，能够将Web自动化操作历史转换为
高质量、生产就绪的Playwright Python代码。遵循项目的Agent架构模式。
"""

import logging
from typing import Optional, List
from .prompts import get_codegen_agent_prompt
from langchain.agents import create_openai_functions_agent, AgentExecutor
from langchain_openai import ChatOpenAI
from langchain_core.tools import BaseTool

from ..core.config import config

logger = logging.getLogger(__name__)


class CodeGenAgent:
    """
    代码生成专家Agent。
    
    专门将Web自动化操作历史转换为Playwright Python代码的智能Agent。
    具备操作序列提取、代码生成、文件管理等完整的代码生成能力。
    
    特性：
    - 智能操作序列识别和提取
    - 生产级Playwright代码生成
    - 完整的错误处理和资源管理
    - 自动文件保存和组织
    - 支持async/sync两种代码风格
    """

    def __init__(
        self,
        tools: Optional[List[BaseTool]] = None,
        verbose: Optional[bool] = None,
        llm_config: Optional[dict] = None,
    ):
        """
        初始化CodeGenAgent。

        Args:
            tools: LangChain工具列表。如果为None，使用默认的代码生成工具。
            verbose: 是否启用详细日志。
            llm_config: 自定义LLM配置。
        """
        if tools is None:
            from ..tools.codegen_tools import get_codegen_tools
            
            self.tools = get_codegen_tools()
            logger.info(
                f"CodeGenAgent initialized with {len(self.tools)} specialized tools: "
                f"extract_and_generate_code, yolo_validate_and_fix, save_generated_code"
            )
        else:
            self.tools = tools

        self.verbose = verbose if verbose is not None else config.VERBOSE
        self.llm_config = llm_config or config.get_llm_config()

        self._llm = None
        self._agent = None
        self._executor = None

    def _create_llm(self) -> ChatOpenAI:
        """创建和配置LLM实例。"""
        if self._llm is None:
            try:
                self._llm = ChatOpenAI(**self.llm_config)
                logger.info(f"LLM created with model: {self.llm_config.get('model')}")
            except Exception as e:
                logger.error(f"Failed to create LLM: {e}")
                raise
        return self._llm

    def _create_agent(self) -> None:
        """创建LangChain agent。"""
        if self._agent is None:
            try:
                llm = self._create_llm()
                prompt = get_codegen_agent_prompt()
                self._agent = create_openai_functions_agent(llm, self.tools, prompt)
                logger.info("CodeGenAgent created successfully")
            except Exception as e:
                logger.error(f"Failed to create agent: {e}")
                raise

    def _create_executor(self) -> AgentExecutor:
        """创建agent executor。"""
        if self._executor is None:
            self._create_agent()
            try:
                self._executor = AgentExecutor(
                    agent=self._agent,
                    tools=self.tools,
                    verbose=self.verbose,
                    max_iterations=10,
                    handle_parsing_errors=True,
                )
                logger.info("CodeGenAgent executor created successfully")
            except Exception as e:
                logger.error(f"Failed to create agent executor: {e}")
                raise
        return self._executor

    def invoke(self, input_text: str) -> dict:
        """
        生成Playwright自动化代码。

        Args:
            input_text: 包含执行历史或代码生成请求的文本

        Returns:
            包含代码生成结果的字典，包括生成的代码、保存路径等信息
            
        Examples:
            # 基于执行历史生成代码
            result = agent.invoke("请根据以下执行历史生成代码：{execution_results: ...}")
            
            # 直接请求代码生成
            result = agent.invoke("生成一个访问booking.com并搜索酒店的自动化脚本")
        """
        executor = self._create_executor()

        try:
            logger.info(f"CodeGenAgent starting code generation for: {input_text[:100]}...")
            result = executor.invoke({"input": input_text})
            logger.info("CodeGenAgent code generation completed successfully")
            return result
        except Exception as e:
            logger.error(f"CodeGenAgent code generation failed: {e}")
            raise

    def generate_from_history(self, execution_results: str, code_style: str = "async") -> dict:
        """
        从执行历史生成代码的便捷方法。
        
        Args:
            execution_results: JSON格式的执行结果字符串
            code_style: 代码风格，"async"或"sync"
            
        Returns:
            代码生成结果字典
        """
        prompt = f"""
请基于以下执行历史生成{code_style}风格的Playwright Python代码：

执行历史：
{execution_results}

要求：
1. 提取所有Web操作序列
2. 生成高质量的{code_style}代码
3. 包含完整的错误处理
4. 保存到generated_code目录
5. 提供详细的执行报告

请立即开始分析和生成。
"""
        return self.invoke(prompt)

    def generate_from_description(self, task_description: str, code_style: str = "async") -> dict:
        """
        从任务描述生成代码的便捷方法。
        
        Args:
            task_description: 任务描述文本
            code_style: 代码风格，"async"或"sync"
            
        Returns:
            代码生成结果字典
        """
        prompt = f"""
请为以下任务生成{code_style}风格的Playwright Python自动化代码：

任务描述：
{task_description}

要求：
1. 分析任务需求，设计操作序列
2. 生成生产级{code_style}代码
3. 包含完整的错误处理和日志
4. 保存到generated_code目录
5. 提供使用指导

请立即开始设计和生成。
"""
        return self.invoke(prompt)

    def cleanup(self) -> None:
        """清理Agent使用的资源。"""
        try:
            self._llm = None
            self._agent = None
            self._executor = None
            logger.info("CodeGenAgent cleanup completed")
        except Exception as e:
            logger.error(f"Error during CodeGenAgent cleanup: {e}")


def build_codegen_agent(
    tools: Optional[List[BaseTool]] = None,
    verbose: Optional[bool] = None,
    llm_config: Optional[dict] = None,
) -> CodeGenAgent:
    """
    构建并返回配置好的CodeGenAgent实例。

    Args:
        tools: LangChain工具列表
        verbose: 是否启用详细日志
        llm_config: 自定义LLM配置

    Returns:
        配置好的CodeGenAgent实例
        
    Example:
        # 创建默认配置的CodeGenAgent
        agent = build_codegen_agent()
        
        # 创建自定义配置的CodeGenAgent
        agent = build_codegen_agent(verbose=True, llm_config={"temperature": 0.1})
    """
    try:
        agent = CodeGenAgent(tools=tools, verbose=verbose, llm_config=llm_config)
        logger.info("CodeGenAgent built successfully")
        return agent
    except Exception as e:
        logger.error(f"Failed to build CodeGenAgent: {e}")
        raise


# 工厂函数别名，提供更直观的API
def create_codegen_agent(**kwargs) -> CodeGenAgent:
    """
    创建CodeGenAgent的别名函数。
    
    Args:
        **kwargs: 传递给build_codegen_agent的参数
        
    Returns:
        CodeGenAgent实例
    """
    return build_codegen_agent(**kwargs)


def get_codegen_agent_tools() -> List[BaseTool]:
    """
    获取CodeGenAgent使用的工具列表。
    
    Returns:
        CodeGenAgent专用工具列表
    """
    from ..tools.codegen_tools import get_codegen_tools
    return get_codegen_tools()


# 模块导出信息
__all__ = [
    "CodeGenAgent",
    "build_codegen_agent", 
    "create_codegen_agent",
    "get_codegen_agent_tools"
]