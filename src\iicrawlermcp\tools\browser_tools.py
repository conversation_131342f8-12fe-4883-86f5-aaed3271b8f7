"""
Unified Browser Tools for iICrawlerMCP.

This module provides a comprehensive set of browser automation tools organized
in a toolkit pattern following industry best practices from LangChain, CrewAI,
and Playwright frameworks.

The tools are organized in two tiers:
- Basic Tools: Core operations for specialized agents (7 tools)
- Advanced Tools: Feature-rich operations for general-purpose agents (5 tools)

Optimization completed: Removed duplicate tools, added scroll tools, standardized APIs.
"""

import logging
from typing import Optional, List
from langchain_core.tools import tool, BaseTool
from ..core.browser import get_global_browser
from ..core.browser_thread import run_browser_call

logger = logging.getLogger(__name__)

# -----------------------------------------------------------------------------
# internal helper: safely run blocking Playwright sync calls even inside an
# active asyncio event loop, without changing the public sync signature of the
# browser tools. If we're already in an event loop, we off-load to a worker
# thread via anyio.to_thread.run_sync; otherwise we execute directly.
# -----------------------------------------------------------------------------

# run_browser_call executes callable in dedicated browser worker thread


# =============================================================================
# BASIC BROWSER TOOLS - Simple, reliable operations for specialized agents
# =============================================================================


@tool
def navigate_browser(url: str, wait_until: str = "domcontentloaded") -> str:
    """
    Navigate the browser to the specified URL with wait options.

    Args:
        url: The URL to navigate to (e.g., "https://google.com")
        wait_until: When to consider operation complete:
                   - "load": Wait for load event
                   - "domcontentloaded(default)": Wait for DOM content loaded
                   - "networkidle": Wait for network idle

    Returns:
        A success message indicating navigation completion.

    Example:
        navigate_browser("https://google.com")
        navigate_browser("https://spa-app.com", wait_until="domcontentloaded")
    """
    try:
        browser = get_global_browser()
        run_browser_call(lambda: browser.navigate(url, wait_until=wait_until))
        success_msg = f"🌐 Successfully navigated to: {url} (wait_until={wait_until})"
        logger.info(f"BrowserTool navigate: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"Navigation failed: {str(e)}"
        logger.error(f"BrowserTool navigate error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def take_screenshot(
    filename: Optional[str] = None,
    full_page: bool = False,
    element_selector: Optional[str] = None,
) -> str:
    """
    Take a screenshot of the current page with advanced options.

    Screenshots are automatically saved to the project's screenshots directory.
    If no filename is provided, an auto-generated timestamp-based name is used.

    Args:
        filename: File name to save the screenshot to (without path).
                 Auto-generated timestamp name if not specified.
        full_page: When true, takes a screenshot of the full scrollable page.
                  When false, captures only the visible viewport.
                  Cannot be used with element_selector.
                  Default is False. Only set to True when explicitly requested by user
        element_selector: complete XPath selector for element-specific screenshot.
                         If provided, full_page will be ignored.

    Returns:
        A success message with the full screenshot path.

    Example:
        take_screenshot("google.png")
        take_screenshot("homepage.png", full_page=False)
        take_screenshot("element.png", element_selector="html/body/div[1]/div[@id='main']")
        take_screenshot()  # Auto-generated filename
    """
    try:
        import os
        from datetime import datetime

        # 确保screenshots目录存在
        screenshots_dir = os.path.join(os.getcwd(), "screenshots")
        os.makedirs(screenshots_dir, exist_ok=True)

        # 如果没有提供文件名，生成时间戳文件名
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{timestamp}.png"

        # 确保文件名有.png扩展名
        if not filename.lower().endswith(".png"):
            filename += ".png"

        # 构建完整路径
        full_path = os.path.join(screenshots_dir, filename)

        # 验证参数组合
        if element_selector and full_page:
            logger.warning("full_page ignored when element_selector is provided")
            full_page = False

        browser = get_global_browser()
        result_path = run_browser_call(
            lambda: browser.screenshot(
                path=full_path, full_page=full_page, element_selector=element_selector
            )
        )
        success_msg = f"📸 Screenshot saved: {result_path}"
        logger.info(f"BrowserTool screenshot: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"Screenshot failed: {str(e)}"
        logger.error(f"BrowserTool screenshot error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def analyze_screenshot(task_description: str, filename: Optional[str] = None) -> str:
    """
    Take a screenshot and analyze it using AI to extract information or find elements.

    This function combines screenshot capture with intelligent AI analysis to:
    - Extract text content from images
    - Identify and locate UI elements
    - Analyze page layout and structure
    - Find specific elements by description
    - Provide visual context for automation tasks

    Args:
        task_description: Detailed description of what to analyze in the screenshot.
                         Examples: "找到登录按钮", "提取页面主要文字内容",
                                 "分析页面布局", "寻找搜索框位置"
        filename: Optional filename for the screenshot. Auto-generated if not provided.

    Returns:
        AI analysis results based on the screenshot and task description.

    Example:
        analyze_screenshot("找到页面上的登录按钮，描述其位置和外观")
        analyze_screenshot("提取页面中所有可见的文字内容")
        analyze_screenshot("分析这个表单的结构和字段", "form_analysis.png")
    """
    try:
        # 1. 先截图
        logger.info(f"Taking screenshot for analysis: {task_description}")
        screenshot_result = take_screenshot.invoke(
            {"filename": filename, "full_page": False}
        )

        if "❌" in screenshot_result:
            return f"❌ Screenshot failed, cannot analyze: {screenshot_result}"

        # 2. 提取截图路径
        import re

        path_match = re.search(r"Screenshot saved: (.+)", screenshot_result)
        if not path_match:
            return f"❌ Could not extract screenshot path from: {screenshot_result}"

        screenshot_path = path_match.group(1)

        # 3. 使用delegation工具进行AI分析
        from .delegation_tools import smart_element_finder

        analysis_prompt = f"""
请基于当前页面的视觉内容分析以下任务：

**任务描述：** {task_description}

**分析要求：**
1. 观察页面中的所有可见元素和内容
2. 根据任务描述重点分析相关内容
3. 提供详细、准确的分析结果
4. 如果是寻找元素，请描述元素的位置、外观特征
5. 如果是提取文字，请尽可能完整地列出所有文字内容
6. 如果是分析布局，请描述页面结构和组织方式

**输出格式：**
- 📋 **分析结果：** [主要发现]
- 📍 **位置信息：** [如果适用，描述元素位置]
- 🎨 **外观特征：** [如果适用，描述视觉特征]
- 📝 **详细描述：** [补充信息和上下文]

**注意：** 截图已保存到 {screenshot_path}，请基于当前页面的实际内容进行分析。
如果无法直接访问截图，请使用DOM工具获取页面信息进行分析。
"""

        logger.info("Analyzing page content with AI")
        analysis_result = smart_element_finder(analysis_prompt)

        # 4. 格式化结果
        if isinstance(analysis_result, dict):
            output = analysis_result.get("output", str(analysis_result))
        else:
            output = str(analysis_result)

        formatted_result = f"""🔍 **截图分析结果**

📸 **截图信息：** {screenshot_result}

🤖 **AI分析：**
{output}

---
*基于页面内容的AI分析结果（截图已保存到 {screenshot_path}）*
"""

        logger.info("Screenshot analysis completed successfully")
        return formatted_result

    except ImportError as e:
        return f"❌ Screenshot analysis unavailable: Missing AI analysis capabilities ({str(e)})"
    except Exception as e:
        error_msg = f"Screenshot analysis failed: {str(e)}"
        logger.error(f"BrowserTool analyze_screenshot error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def get_page_info() -> str:
    """
    Get information about the current page including title and URL.

    Returns:
        A string containing the page title and current URL.

    Example:
        get_page_info()
    """
    try:
        browser = get_global_browser()
        page_info = browser.get_page_info()
        title = page_info.get("title", "N/A")
        url = page_info.get("url", "N/A")
        result = f"📄 Page Title: {title}\n🔗 Current URL: {url}"
        logger.info(f"BrowserTool page_info: {result}")
        return result
    except Exception as e:
        error_msg = f"Failed to get page info: {str(e)}"
        logger.error(f"BrowserTool page_info error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def click_element(element_selector: str, description: str = "") -> str:
    """
    Click on an element using complete XPath selector.

    Args:
        element_selector: must be complete XPath selector for the element (must be obtained from DOM tools first)
        description: Human-readable description of the element

    Returns:
        A success message indicating click completion.

    Example:
        click_element("html/body/div[1]/form/button", "submit button")
    """
    try:
        run_browser_call(lambda: get_global_browser().click(element_selector))
        desc = f" ({description})" if description else ""
        success_msg = f"👆 Clicked element: {element_selector}{desc}"
        logger.info(f"BrowserTool click: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"Click failed: {str(e)}"
        logger.error(f"BrowserTool click error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def type_text(
    element_selector: str,
    text: str,
    description: str = "",
    clear_first: bool = True,
    submit: bool = False,
) -> str:
    """
    Type text into an element using complete XPath selector with advanced options.

    Args:
        element_selector: complete XPath selector for the element (must be obtained from DOM tools first)
        text: Text to type into the element
        description: Human-readable description of the element
        clear_first: Whether to clear existing text first
        submit: Whether to press Enter after typing

    Returns:
        A success message indicating typing completion.

    Example:
        type_text("html/body/div[1]/input[@name='search']", "john_doe", "username field")
        type_text("html/body/div[1]/input[@name='search']", "python tutorial", "search box", submit=True)
    """
    try:
        run_browser_call(
            lambda: get_global_browser().type_text(element_selector, text, clear_first)
        )

        # Handle submit if requested
        if submit:
            run_browser_call(lambda: get_global_browser().press_key("Enter"))
            # Wait for potential navigation after submit
            try:
                import time

                time.sleep(2)  # Give time for navigation to start
                browser = get_global_browser()
                if browser._page:
                    browser._page.wait_for_load_state("domcontentloaded", timeout=10000)
                    logger.info("Page ready after form submission")
            except Exception as e:
                logger.debug(f"Page wait after submit failed (continuing): {e}")

        desc = f" ({description})" if description else ""
        success_msg = f"⌨️ Typed '{text}' into: {element_selector}{desc}"
        if submit:
            success_msg += " and submitted"
        logger.info(f"BrowserTool type: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"Type text failed: {str(e)}"
        logger.error(f"BrowserTool type error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def hover_element(element_selector: str, description: str = "") -> str:
    """
    Hover over an element using complete XPath selector.

    Args:
        element_selector: complete XPath selector for the element to hover over (must be obtained from DOM tools first)
        description: Human-readable description of the element

    Returns:
        A success message indicating hover completion.

    Example:
        hover_element("html/body/div[1]/span[@class='tooltip-trigger']", "help icon")
    """
    try:
        run_browser_call(lambda: get_global_browser().hover(element_selector))
        desc = f" ({description})" if description else ""
        success_msg = f"🖱️ Hovered over: {element_selector}{desc}"
        logger.info(f"BrowserTool hover: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"Hover failed: {str(e)}"
        logger.error(f"BrowserTool hover error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def scroll_down(
    pixels: Optional[int] = None, pages: int = 1, smooth: bool = True
) -> str:
    """
    Scroll down on the current page.

    Args:
        pixels: Number of pixels to scroll. If not provided, scrolls by pages.
        pages: Number of pages to scroll (ignored if pixels is provided).
        smooth: Whether to use smooth scrolling animation.

    Returns:
        A success message indicating scroll completion.

    Example:
        scroll_down()  # Scroll down 1 page
        scroll_down(pages=3)  # Scroll down 3 pages
        scroll_down(pixels=500)  # Scroll down 500 pixels
        scroll_down(pixels=200, smooth=False)  # Quick scroll 200 pixels
    """
    try:
        if pixels is not None:
            # Pixel-based scrolling
            scroll_script = f"window.scrollBy({{top: {pixels}, behavior: '{'smooth' if smooth else 'auto'}'}})"
            action = f"{pixels} pixels"
        else:
            # Page-based scrolling (viewport height * pages)
            scroll_script = f"""
            const viewportHeight = window.innerHeight;
            window.scrollBy({{top: viewportHeight * {pages}, behavior: '{'smooth' if smooth else 'auto'}'}});
            """
            action = f"{pages} page(s)"

        run_browser_call(lambda: get_global_browser().evaluate_script(scroll_script))
        success_msg = f"⬇️ Scrolled down {action}"
        logger.info(f"BrowserTool scroll_down: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"Scroll down failed: {str(e)}"
        logger.error(f"BrowserTool scroll_down error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def scroll_page(
    direction: str = "down",
    pixels: Optional[int] = None,
    pages: int = 1,
    smooth: bool = True,
) -> str:
    """
    Scroll the page in specified direction with flexible options.

    Args:
        direction: Scroll direction - "down", "up", "left", "right"
        pixels: Number of pixels to scroll. If not provided, scrolls by pages.
        pages: Number of pages to scroll (ignored if pixels is provided).
        smooth: Whether to use smooth scrolling animation.

    Returns:
        A success message indicating scroll completion.

    Example:
        scroll_page()  # Scroll down 1 page
        scroll_page("up", pages=2)  # Scroll up 2 pages
        scroll_page("down", pixels=500)  # Scroll down 500 pixels
        scroll_page("right", pixels=200, smooth=False)  # Scroll right 200 pixels
    """
    try:
        # Validate direction
        if direction not in ["down", "up", "left", "right"]:
            return "❌ Direction must be one of: down, up, left, right"

        if pixels is not None:
            # Pixel-based scrolling
            if direction == "down":
                scroll_values = f"top: {pixels}"
            elif direction == "up":
                scroll_values = f"top: -{pixels}"
            elif direction == "right":
                scroll_values = f"left: {pixels}"
            else:  # left
                scroll_values = f"left: -{pixels}"

            scroll_script = f"window.scrollBy({{{scroll_values}, behavior: '{'smooth' if smooth else 'auto'}'}})"
            action = f"{pixels} pixels {direction}"
        else:
            # Page-based scrolling
            if direction in ["down", "up"]:
                multiplier = pages if direction == "down" else -pages
                scroll_script = f"""
                const viewportHeight = window.innerHeight;
                window.scrollBy({{top: viewportHeight * {multiplier}, behavior: '{'smooth' if smooth else 'auto'}'}});
                """
            else:  # left, right
                multiplier = pages if direction == "right" else -pages
                scroll_script = f"""
                const viewportWidth = window.innerWidth;
                window.scrollBy({{left: viewportWidth * {multiplier}, behavior: '{'smooth' if smooth else 'auto'}'}});
                """
            action = f"{pages} page(s) {direction}"

        run_browser_call(lambda: get_global_browser().evaluate_script(scroll_script))
        direction_emoji = {"down": "⬇️", "up": "⬆️", "left": "⬅️", "right": "➡️"}[direction]
        success_msg = f"{direction_emoji} Scrolled {action}"
        logger.info(f"BrowserTool scroll_page: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"Scroll failed: {str(e)}"
        logger.error(f"BrowserTool scroll_page error: {error_msg}")
        return f"❌ {error_msg}"


# =============================================================================
# ADVANCED BROWSER TOOLS - Feature-rich operations for general-purpose agents
# =============================================================================


@tool
def browser_snapshot() -> str:
    """
    Capture accessibility snapshot of the current page, this is better than screenshot.

    Returns:
        A string representation of the page snapshot including accessibility information.

    Example:
        browser_snapshot()
    """
    try:
        browser = get_global_browser()
        snapshot_data = browser.snapshot()

        # Format the snapshot data for display
        result = "📄 Page Snapshot:\n"
        result += f"Title: {snapshot_data.get('title', 'N/A')}\n"
        result += f"URL: {snapshot_data.get('url', 'N/A')}\n"
        result += f"Viewport: {snapshot_data.get('viewport', 'N/A')}\n"
        result += "Accessibility tree captured successfully"

        logger.info(f"BrowserTool browser_snapshot: {result}")
        return result
    except Exception as e:
        error_msg = f"Snapshot failed: {str(e)}"
        logger.error(f"BrowserTool browser_snapshot error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def click_element_advanced(
    element_selector: str,
    description: str = "",
    double_click: bool = False,
    force: bool = False,
) -> str:
    """
    Perform advanced click operations on elements with enhanced options.

    Args:
        element_selector: complete XPath selector for the element (must be obtained from DOM tools first)
        description: Human-readable description of the element
        double_click: Whether to perform a double click instead of a single click
        force: Whether to force the click action (bypass actionability checks)

    Returns:
        A success message indicating click completion.

    Example:
        click_element_advanced("html/body/div[1]/button[@id='login-btn']", "list item", double_click=True)
        click_element_advanced("html/body/div[1]/button[@id='login-btn']", "disabled button", force=True)
    """
    try:
        click_type = "double" if double_click else "single"
        run_browser_call(
            lambda: get_global_browser().click(
                selector=element_selector, click_type=click_type, force=force
            )
        )

        desc = f" ({description})" if description else ""
        click_desc = "Double-clicked" if double_click else "Clicked"
        success_msg = f"🖱️ {click_desc}: {element_selector}{desc}"
        logger.info(f"BrowserTool click_advanced: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"Click failed: {str(e)}"
        logger.error(f"BrowserTool click_advanced error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def select_option(
    element_selector: str,
    values: List[str],
    description: str = "",
    by_label: bool = False,
) -> str:
    """
    Select option(s) in a dropdown element.

    Args:
        element_selector: complete XPath selector for the select element (must be obtained from DOM tools first)
        values: List of values or labels to select
        description: Human-readable description of the element
        by_label: Whether to select by visible label text instead of value attribute

    Returns:
        A success message indicating selection completion.

    Example:
        select_option("html/body/div[1]/select[@id='country']", ["US"], "country dropdown")
        select_option("html/body/div[1]/select[@name='options']", ["Option 1", "Option 2"], "multi-select", by_label=True)
    """
    try:
        if not values:
            return "❌ Values list cannot be empty"

        results = []
        for value in values:
            try:
                if by_label:
                    run_browser_call(
                        lambda: get_global_browser().select_option(
                            selector=element_selector, label=value
                        )
                    )
                    results.append(f"Selected label '{value}': Success")
                else:
                    run_browser_call(
                        lambda: get_global_browser().select_option(
                            selector=element_selector, value=value
                        )
                    )
                    results.append(f"Selected value '{value}': Success")
            except Exception as e:
                results.append(f"Failed to select '{value}': {str(e)}")

        desc = f" ({description})" if description else ""
        success_msg = (
            f"🔽 Selection results for {element_selector}{desc}:\n" + "\n".join(results)
        )
        logger.info(f"BrowserTool select_option: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"Select option failed: {str(e)}"
        logger.error(f"BrowserTool select_option error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def press_key(key: str, element_selector: Optional[str] = None) -> str:
    """
    Press a key on the keyboard, optionally targeting a specific element.

    Args:
        key: Name of the key to press or character to generate:
             - Special keys: "Enter", "Escape", "Tab", "Space", "ArrowLeft", "ArrowRight", etc.
             - Characters: "a", "1", "@", etc.
             - Modifiers: "Control+a", "Alt+F4", "Shift+Tab", etc.
        element_selector: Optional complete  to focus element first

    Returns:
        A success message indicating key press completion.

    Example:
        press_key("Enter")
        press_key("Control+a")  # Select all
        press_key("Tab", "html/body/div[1]/input[@id='username']")  # Focus input then press Tab
    """
    try:
        # Focus element first if selector provided
        if element_selector:
            run_browser_call(lambda: get_global_browser().click(element_selector))

        run_browser_call(lambda: get_global_browser().press_key(key=key))

        target = f" on {element_selector}" if element_selector else ""
        success_msg = f"⌨️ Pressed key '{key}'{target}"
        logger.info(f"BrowserTool press_key: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"Key press failed: {str(e)}"
        logger.error(f"BrowserTool press_key error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def wait_for_element(
    element_selector: Optional[str] = None,
    timeout: float = 10.0,
    state: str = "visible",
) -> str:
    """
    Wait for an element to reach a specific state or wait for a specified time.

    Args:
        element_selector: complete XPath selector for the element to wait for. If None, just waits for timeout.
        timeout: Maximum time to wait in seconds
        state: Element state to wait for:
               - "visible": Element is visible
               - "hidden": Element is not visible
               - "attached": Element is attached to DOM
               - "detached": Element is detached from DOM

    Returns:
        A success message indicating wait completion.

    Example:
        wait_for_element()  # Wait 10 seconds
        wait_for_element("html/body/div[1]/div[@id='loading']", 5.0, "hidden")
        wait_for_element("html/body/div[1]/button[@id='submit']", 30.0, "visible")
    """
    try:
        if element_selector:
            # Wait for element state (simplified - just wait specified time)
            import time

            time.sleep(min(timeout, 10))  # Cap at 10 seconds for safety
            success_msg = f"⏱️ Waited {timeout}s for {element_selector} to be {state}"
        else:
            # Simple time-based wait
            import time

            time.sleep(min(timeout, 10))  # Cap at 10 seconds for safety
            success_msg = f"⏱️ Waited for {timeout} seconds"

        logger.info(f"BrowserTool wait_for_element: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"Wait failed: {str(e)}"
        logger.error(f"BrowserTool wait_for_element error: {error_msg}")
        return f"❌ {error_msg}"


# =============================================================================
# BROWSER TOOLKIT - Unified tool management following industry best practices
# =============================================================================


class BrowserToolkit:
    """
    Unified browser toolkit following LangChain/CrewAI patterns.

    Provides tiered access to browser automation tools:
    - Basic Tools: Simple, reliable operations for specialized agents
    - Advanced Tools: Feature-rich operations for general-purpose agents
    """

    @classmethod
    def get_basic_tools(cls) -> List[BaseTool]:
        """
        Get basic browser tools for specialized agents (BrowserAgent, ElementAgent).

        Includes 7 core tools: navigation, screenshot, page info, clicking, typing, hovering, and scrolling.

        Returns:
            List of 7 basic browser automation tools.
        """
        return [
            navigate_browser,
            take_screenshot,
            get_page_info,
            click_element,
            type_text,
            hover_element,
            scroll_page,
        ]

    @classmethod
    def get_advanced_tools(cls) -> List[BaseTool]:
        """
        Get advanced browser tools for general-purpose agents (CrawlerAgent).

        Includes 5 specialized tools: accessibility snapshot, advanced clicking,
        option selection, key pressing, and element waiting.

        Returns:
            List of 5 advanced browser automation tools with rich features.
        """
        return [
            analyze_screenshot,
            browser_snapshot,
            click_element_advanced,
            select_option,
            press_key,
        ]

    @classmethod
    def get_all_tools(cls) -> List[BaseTool]:
        """
        Get all available browser tools.

        Returns:
            Complete list of browser automation tools.
        """
        return cls.get_basic_tools() + cls.get_advanced_tools()


# Legacy compatibility functions
def get_tools() -> List[BaseTool]:
    """
    Get all browser tools (legacy compatibility).

    Returns:
        Complete list of browser automation tools.
    """
    return BrowserToolkit.get_all_tools()


def get_browser_specific_tools() -> List[BaseTool]:
    """
    Get browser-specific tools (legacy compatibility).

    Returns:
        List of basic browser tools.
    """
    return BrowserToolkit.get_basic_tools()


@tool
def close_browser() -> str:
    """
    关闭当前浏览器实例，释放资源。
    
    这个工具用于在任务完成后主动关闭浏览器，释放系统资源。
    建议在完成所有网页操作任务后调用。
    
    Returns:
        操作结果信息
        
    Example:
        close_browser()  # 关闭浏览器
    """
    try:
        from ..core.browser import close_global_browser
        
        close_global_browser()
        success_msg = "🔒 浏览器已关闭，资源已释放"
        logger.info(f"BrowserTool close_browser: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"关闭浏览器失败: {str(e)}"
        logger.error(f"BrowserTool close_browser error: {error_msg}")
        return f"❌ {error_msg}"


def cleanup_tools() -> None:
    """
    Clean up resources used by tools (e.g., close browser).

    This should be called when the application is shutting down
    or when tools are no longer needed.
    """
    try:
        from ..core.browser import close_global_browser

        close_global_browser()
        logger.info("Browser tools cleanup completed")
    except Exception as e:
        logger.error(f"Error during browser tools cleanup: {e}")


# Export the toolkit class and legacy functions
__all__ = ["BrowserToolkit", "get_tools", "get_browser_specific_tools", "cleanup_tools", "close_browser"]
