"""
LangGraph状态管理模块

定义统一的爬虫状态，用于在Agent间共享上下文和执行历史。
这是迁移到LangGraph架构的核心组件。
"""

from typing import List, Dict, Any, Optional, Annotated, TypedDict
from langchain_core.messages import BaseMessage
from langgraph.graph import MessagesState
import operator
import hashlib
import json


class CrawlerState(MessagesState):
    """
    统一的爬虫状态定义

    继承自MessagesState以利用LangGraph内置的消息去重和流式传输优化。
    这个状态在整个爬虫流程中共享，自动管理Agent间的上下文传递。
    解决了原有架构中每次创建新Agent实例导致的上下文丢失问题。
    """
    # messages字段由MessagesState提供，自动处理去重和流式传输

    # 当前页面URL
    current_url: Optional[str]

    # DOM状态缓存（避免重复提取）
    dom_state: Optional[Dict[str, Any]]

    # 任务执行计划
    task_plan: Optional[Dict[str, Any]]

    # 当前活跃的Agent名称
    active_agent: str

    # 执行结果累积（使用operator.add自动累积）
    execution_results: Annotated[List[Dict[str, Any]], operator.add]

    # 共享上下文变量（用于Agent间传递特定信息）
    context_variables: Dict[str, Any]

    # 错误信息
    error: Optional[str]

    # 重试次数
    retry_count: int

    # 任务完成状态
    is_complete: Annotated[bool, lambda x, y: y if y is not None else x]


class AgentTransition(TypedDict):
    """
    Agent切换时的过渡状态

    用于记录Agent间的切换决策和原因。
    """

    from_agent: str
    to_agent: str
    reason: str
    handoff_data: Dict[str, Any]


def create_initial_state(task_description: str) -> CrawlerState:
    """
    创建初始状态

    Args:
        task_description: 用户的任务描述

    Returns:
        初始化的爬虫状态
    """
    from langchain_core.messages import HumanMessage

    return {
        "messages": [HumanMessage(content=task_description)],
        "current_url": None,
        "dom_state": None,
        "task_plan": None,
        "active_agent": "planner",  # 默认从规划器开始
        "execution_results": [],
        "context_variables": {},
        "error": None,
        "retry_count": 0,
        "is_complete": False,
    }


def update_state_for_handoff(
    current_state: CrawlerState, target_agent: str, handoff_data: Dict[str, Any] = None
) -> Dict[str, Any]:
    """
    为Agent切换准备状态更新

    Args:
        current_state: 当前状态
        target_agent: 目标Agent名称
        handoff_data: 需要传递的额外数据

    Returns:
        状态更新字典
    """
    updates = {
        "active_agent": target_agent,
    }

    if handoff_data:
        # 合并到context_variables中
        new_context = current_state.get("context_variables", {}).copy()
        new_context.update(handoff_data)
        updates["context_variables"] = new_context

    return updates


def extract_agent_context(state: CrawlerState, agent_name: str) -> Dict[str, Any]:
    """
    为特定Agent提取相关上下文

    不同的Agent可能需要不同的上下文视图，这个函数提供定制化的上下文提取。

    Args:
        state: 完整状态
        agent_name: Agent名称

    Returns:
        Agent特定的上下文
    """
    base_context = {
        "messages": state["messages"][-10:],  # 只保留最近10条消息
        "current_url": state["current_url"],
        "task_description": state["messages"][0].content if state["messages"] else "",
    }

    # 根据不同Agent定制上下文
    if agent_name == "browser":
        # BrowserAgent需要DOM状态
        base_context["dom_state"] = state.get("dom_state")
        base_context["last_action"] = state["context_variables"].get(
            "last_browser_action"
        )

    elif agent_name == "element":
        # ElementAgent需要DOM和目标元素信息
        base_context["dom_state"] = state.get("dom_state")
        base_context["target_elements"] = state["context_variables"].get(
            "target_elements"
        )

    elif agent_name == "planner":
        # PlannerAgent需要执行历史
        base_context["execution_results"] = state["execution_results"]
        base_context["retry_count"] = state["retry_count"]

    elif agent_name == "validator":
        # ValidatorAgent需要完整执行结果
        base_context["task_plan"] = state.get("task_plan")
        base_context["execution_results"] = state["execution_results"]

    return base_context
