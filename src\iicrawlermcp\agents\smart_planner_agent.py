"""
Smart Planner Agent module for unified task planning and prompt optimization.

This module provides a specialized agent that combines the capabilities of 
task planning and prompt optimization. It analyzes user intent, generates
clarifying questions, and creates optimized execution plans.
"""

import json
import logging
from typing import Optional, List, Dict, Any
from .prompts import get_smart_planner_prompt
from langchain.agents import create_openai_functions_agent, AgentExecutor
from langchain_openai import ChatOpenAI
from langchain_core.tools import BaseTool

from ..core.config import config

logger = logging.getLogger(__name__)


class SmartPlannerAgent:
    """
    统一的智能规划Agent，整合了提示词优化和任务规划功能。
    
    这个Agent负责：
    1. 理解用户意图和任务复杂度分析
    2. 识别模糊点并生成澄清问题
    3. 搜索相关信息辅助规划决策
    4. 生成优化的任务描述和执行计划
    5. 提供风险评估和成功标准
    
    相比于分离的PlannerAgent和PromptOptimizationAgent，SmartPlannerAgent提供：
    - 统一的工作流程，减少Agent间通信开销
    - 更智能的意图理解和复杂度分析
    - 集成的搜索能力，获取实时信息
    - 优化的提示词生成和执行计划制定
    """

    def __init__(
        self,
        tools: Optional[List[BaseTool]] = None,
        verbose: Optional[bool] = None,
        llm_config: Optional[dict] = None,
    ):
        """
        Initialize the SmartPlannerAgent.

        Args:
            tools: List of LangChain tools to use. If None, uses default smart planner tools.
            verbose: Whether to enable verbose logging.
            llm_config: Custom LLM configuration.
        """
        if tools is None:
            from ..tools.smart_planner_tools import get_smart_planner_tools
            self.tools = get_smart_planner_tools()
            logger.info(f"SmartPlannerAgent initialized with {len(self.tools)} tools: {[t.name for t in self.tools]}")
        else:
            self.tools = tools

        self.verbose = verbose if verbose is not None else config.VERBOSE
        self.llm_config = llm_config or config.get_llm_config()

        # Lazy initialization
        self._llm = None
        self._agent = None
        self._executor = None
        
        # 内部状态管理
        self._last_analysis = None
        self._last_plan = None
        self._conversation_history = []

    def _create_llm(self) -> ChatOpenAI:
        """Create and configure the LLM instance."""
        if self._llm is None:
            try:
                self._llm = ChatOpenAI(**self.llm_config)
                logger.info(f"SmartPlannerAgent LLM created with model: {self.llm_config.get('model')}")
            except Exception as e:
                logger.error(f"Failed to create LLM: {e}")
                raise
        return self._llm

    def _create_agent(self) -> None:
        """Create the LangChain agent."""
        if self._agent is None:
            try:
                llm = self._create_llm()
                prompt = get_smart_planner_prompt()
                self._agent = create_openai_functions_agent(llm, self.tools, prompt)
                logger.info("SmartPlannerAgent created successfully")
            except Exception as e:
                logger.error(f"Failed to create SmartPlannerAgent: {e}")
                raise

    def _create_executor(self) -> AgentExecutor:
        """Create the agent executor."""
        if self._executor is None:
            self._create_agent()
            try:
                self._executor = AgentExecutor(
                    agent=self._agent,
                    tools=self.tools,
                    verbose=self.verbose,
                    max_iterations=15,  # 稍微增加，因为整合了更多功能
                    handle_parsing_errors=True,
                    return_intermediate_steps=True,  # 返回中间步骤便于调试
                )
                logger.info("SmartPlannerAgent executor created successfully")
            except Exception as e:
                logger.error(f"Failed to create SmartPlannerAgent executor: {e}")
                raise
        return self._executor

    def analyze_task(self, user_input: str) -> Dict[str, Any]:
        """
        分析用户任务，理解意图和复杂度。
        
        Args:
            user_input: 用户的任务描述
            
        Returns:
            包含分析结果的字典
        """
        try:
            logger.info(f"SmartPlannerAgent analyzing task: {user_input[:100]}...")
            
            # 直接调用understand_and_analyze工具
            from ..tools.smart_planner_tools import understand_and_analyze
            analysis_result = understand_and_analyze.invoke({"user_input": user_input})
            
            # 解析结果
            try:
                analysis_data = json.loads(analysis_result)
                self._last_analysis = analysis_data
                logger.info(f"Task analysis completed. Complexity: {analysis_data.get('complexity', 'unknown')}")
                return analysis_data
            except json.JSONDecodeError:
                logger.warning("Failed to parse analysis result as JSON")
                return {"error": "分析结果解析失败", "raw_result": analysis_result}
                
        except Exception as e:
            logger.error(f"Task analysis failed: {e}")
            return {"error": str(e)}

    def generate_plan(self, analysis_result: Optional[Dict] = None, additional_context: str = "") -> Dict[str, Any]:
        """
        生成智能执行计划。
        
        Args:
            analysis_result: 任务分析结果，如果为None则使用上次分析结果
            additional_context: 额外的上下文信息
            
        Returns:
            包含执行计划的字典
        """
        try:
            if analysis_result is None:
                analysis_result = self._last_analysis
                
            if analysis_result is None:
                return {"error": "没有可用的分析结果，请先调用analyze_task"}
            
            logger.info("SmartPlannerAgent generating execution plan...")
            
            # 调用generate_smart_plan工具
            from ..tools.smart_planner_tools import generate_smart_plan
            analysis_json = json.dumps(analysis_result, ensure_ascii=False)
            plan_result = generate_smart_plan.invoke({
                "analysis_result": analysis_json,
                "additional_context": additional_context
            })
            
            # 解析计划结果
            try:
                plan_data = json.loads(plan_result)
                self._last_plan = plan_data
                steps_count = len(plan_data.get("execution_steps", []))
                logger.info(f"Execution plan generated with {steps_count} steps")
                return plan_data
            except json.JSONDecodeError:
                logger.warning("Failed to parse plan result as JSON")
                return {"error": "计划结果解析失败", "raw_result": plan_result}
                
        except Exception as e:
            logger.error(f"Plan generation failed: {e}")
            return {"error": str(e)}

    def search_context(self, query: str, num_results: int = 5) -> str:
        """
        搜索相关上下文信息。
        
        Args:
            query: 搜索查询
            num_results: 结果数量
            
        Returns:
            搜索结果字符串
        """
        try:
            logger.info(f"SmartPlannerAgent searching for: {query}")
            
            # 调用web_search_serp工具
            for tool in self.tools:
                if tool.name == "web_search_serp":
                    result = tool.invoke({"query": query, "num_results": num_results})
                    logger.info(f"Search completed for: {query}")
                    return result
                    
            return "搜索工具不可用"
            
        except Exception as e:
            logger.error(f"Search failed: {e}")
            return f"搜索失败: {str(e)}"

    def invoke(self, input_text: str, context: Optional[str] = None) -> dict:
        """
        统一入口：分析任务并生成优化的执行计划。

        Args:
            input_text: 用户输入的任务描述
            context: 额外的上下文信息

        Returns:
            包含完整分析和计划的字典
        """
        executor = self._create_executor()

        try:
            logger.info(f"SmartPlannerAgent processing: {input_text[:100]}...")
            
            # 构建输入上下文
            full_input = input_text
            if context:
                full_input += f"\n\n额外上下文：{context}"
            
            # 记录对话历史
            self._conversation_history.append({
                "type": "user_input",
                "content": input_text,
                "context": context,
                "timestamp": logger.handlers[0].formatter.formatTime(logging.getLogger().makeRecord("", 0, "", 0, "", None, None)) if logger.handlers else "unknown"
            })
            
            result = executor.invoke({"input": full_input})
            
            # 记录结果
            self._conversation_history.append({
                "type": "agent_output",
                "content": result.get("output", ""),
                "intermediate_steps": len(result.get("intermediate_steps", [])),
                "timestamp": logger.handlers[0].formatter.formatTime(logging.getLogger().makeRecord("", 0, "", 0, "", None, None)) if logger.handlers else "unknown"
            })
            
            logger.info("SmartPlannerAgent processing completed successfully")
            
            # 增强结果信息
            enhanced_result = result.copy()
            enhanced_result.update({
                "agent_type": "SmartPlannerAgent",
                "analysis_available": self._last_analysis is not None,
                "plan_available": self._last_plan is not None,
                "tools_used": [step[0].tool for step in result.get("intermediate_steps", []) if len(step) > 0],
                "conversation_length": len(self._conversation_history)
            })
            
            return enhanced_result
            
        except Exception as e:
            logger.error(f"SmartPlannerAgent processing failed: {e}")
            error_result = {
                "error": str(e),
                "agent_type": "SmartPlannerAgent",
                "input": input_text,
                "context": context
            }
            
            # 记录错误
            self._conversation_history.append({
                "type": "error",
                "content": str(e),
                "input": input_text,
                "timestamp": logger.handlers[0].formatter.formatTime(logging.getLogger().makeRecord("", 0, "", 0, "", None, None)) if logger.handlers else "unknown"
            })
            
            return error_result

    def get_analysis(self) -> Optional[Dict[str, Any]]:
        """获取最近的任务分析结果。"""
        return self._last_analysis

    def get_plan(self) -> Optional[Dict[str, Any]]:
        """获取最近的执行计划。"""
        return self._last_plan

    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """获取对话历史记录。"""
        return self._conversation_history.copy()

    def clear_history(self) -> None:
        """清除对话历史和缓存。"""
        self._conversation_history = []
        self._last_analysis = None
        self._last_plan = None
        logger.info("SmartPlannerAgent history and cache cleared")

    def get_status(self) -> Dict[str, Any]:
        """
        获取Agent状态信息。
        
        Returns:
            包含Agent状态的字典
        """
        return {
            "agent_type": "SmartPlannerAgent",
            "tools_count": len(self.tools),
            "tools": [tool.name for tool in self.tools],
            "llm_model": self.llm_config.get("model", "unknown"),
            "verbose": self.verbose,
            "has_analysis": self._last_analysis is not None,
            "has_plan": self._last_plan is not None,
            "conversation_items": len(self._conversation_history),
            "initialized": {
                "llm": self._llm is not None,
                "agent": self._agent is not None,
                "executor": self._executor is not None
            }
        }

    def cleanup(self) -> None:
        """Clean up resources used by the agent."""
        try:
            self._llm = None
            self._agent = None
            self._executor = None
            self._last_analysis = None
            self._last_plan = None
            self._conversation_history = []
            logger.info("SmartPlannerAgent cleanup completed")
        except Exception as e:
            logger.error(f"Error during SmartPlannerAgent cleanup: {e}")


def build_smart_planner_agent(
    tools: Optional[List[BaseTool]] = None,
    verbose: Optional[bool] = None,
    llm_config: Optional[dict] = None,
) -> SmartPlannerAgent:
    """
    Build and return a configured SmartPlannerAgent instance.

    Args:
        tools: List of LangChain tools to use
        verbose: Whether to enable verbose logging
        llm_config: Custom LLM configuration

    Returns:
        A configured SmartPlannerAgent instance
        
    Example:
        >>> agent = build_smart_planner_agent(verbose=True)
        >>> result = agent.invoke("爬取京东手机商品数据")
        >>> analysis = agent.get_analysis()
        >>> plan = agent.get_plan()
    """
    try:
        agent = SmartPlannerAgent(tools=tools, verbose=verbose, llm_config=llm_config)
        logger.info("SmartPlannerAgent built successfully")
        return agent
    except Exception as e:
        logger.error(f"Failed to build SmartPlannerAgent: {e}")
        raise


# 便捷函数
def quick_analyze(user_input: str, verbose: bool = False) -> Dict[str, Any]:
    """
    快速分析用户输入的便捷函数。
    
    Args:
        user_input: 用户任务描述
        verbose: 是否启用详细日志
        
    Returns:
        分析结果字典
    """
    agent = build_smart_planner_agent(verbose=verbose)
    try:
        return agent.analyze_task(user_input)
    finally:
        agent.cleanup()


def quick_plan(user_input: str, context: str = "", verbose: bool = False) -> Dict[str, Any]:
    """
    快速生成执行计划的便捷函数。
    
    Args:
        user_input: 用户任务描述
        context: 额外上下文
        verbose: 是否启用详细日志
        
    Returns:
        执行计划字典
    """
    agent = build_smart_planner_agent(verbose=verbose)
    try:
        analysis = agent.analyze_task(user_input)
        if "error" not in analysis:
            return agent.generate_plan(analysis, context)
        else:
            return analysis
    finally:
        agent.cleanup()


if __name__ == "__main__":
    # 测试SmartPlannerAgent
    print("Testing SmartPlannerAgent...")
    
    # 快速测试
    test_input = "爬取淘宝商品数据"
    analysis = quick_analyze(test_input, verbose=True)
    print(f"Analysis result: {analysis}")
    
    # 完整测试
    agent = build_smart_planner_agent(verbose=True)
    result = agent.invoke(test_input)
    print(f"Full result: {result}")
    print(f"Agent status: {agent.get_status()}")
    
    agent.cleanup()